'use client';

import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Clock, Calendar, Users, ChevronRight, AlertCircle, CheckCircle, XCircle, PauseCircle, Eye, Trash2, FileText, Send, Loader2 } from 'lucide-react';
import { PMORecord, PMORecordStatus, PMORecordPriority, AgenticTeamId } from '../../lib/agents/pmo/PMOInterfaces';
import { deletePMORecord } from '../../lib/firebase/pmoCollection';
import { useAuth } from '../../app/context/AuthContext';
import MarkdownRenderer from '../MarkdownRenderer'; // Ensure this path is correct
import { toast } from '../../components/ui/use-toast';
import { dispatchTabNavigationEvent } from '../../lib/services/TabNavigationService';

interface PMORecordListProps {
  records: PMORecord[];
  onRefresh: () => void;
}

const PMORecordList: React.FC<PMORecordListProps> = ({ records, onRefresh }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [expandedRecordId, setExpandedRecordId] = useState<string | null>(null);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const [isCreatingRequirement, setIsCreatingRequirement] = useState(false);
  const [requirementCreated, setRequirementCreated] = useState<Record<string, boolean>>({});
  const [isSendingToTeam, setIsSendingToTeam] = useState(false);
  const [sentToTeam, setSentToTeam] = useState<Record<string, boolean>>({});
  const [monitoringIntervals, setMonitoringIntervals] = useState<Record<string, NodeJS.Timeout>>({});


  // Get status icon based on record status
  const getStatusIcon = (status: PMORecordStatus) => {
    switch (status) {
      case 'Draft':
        return <PauseCircle className="w-5 h-5 text-gray-400" />;
      case 'In Progress':
        return <Clock className="w-5 h-5 text-blue-400" />;
      case 'Completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'Cancelled':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-400" />;
    }
  };

  // Get priority color class
  const getPriorityColorClass = (priority: PMORecordPriority) => {
    switch (priority) {
      case 'Low':
        return 'bg-green-500/20 text-green-400';
      case 'Medium':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'High':
        return 'bg-orange-500/20 text-orange-400';
      case 'Critical':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  // Get team name from ID (memoized with useCallback as it's stable)
  const getTeamName = useCallback((teamId: AgenticTeamId): string => {
    switch (teamId) {
      case AgenticTeamId.Marketing:
        return 'Marketing';
      case AgenticTeamId.Research:
        return 'Research';
      case AgenticTeamId.SoftwareDesign:
        return 'Software Design';
      case AgenticTeamId.Sales:
        return 'Sales';
      case AgenticTeamId.BusinessAnalysis:
        return 'Business Analysis';
      case AgenticTeamId.InvestigativeResearch:
        return 'Investigative Research';
      default:
        return 'Unknown Team';
    }
  }, []);

  // Helper function to parse team name from assessment string
  const getProposedTeamNameFromAssessment = useCallback((assessment: string | undefined): string | null => {
    if (!assessment) return null;
    const teamsRegex = /\*\*Teams:\*\*\s*([A-Za-z\s]+?)\s*(?:\*\*Rationale:\*\*|$)/i;
    const match = assessment.match(teamsRegex);
    if (match && match[1]) {
      return match[1].trim();
    }
    return null;
  }, []);

  // Helper function to convert team name string to AgenticTeamId
  const getTeamIdFromName = useCallback((name: string): AgenticTeamId | null => {
    const normalizedName = name.trim().toLowerCase();
    const teamMappings: { [key: string]: AgenticTeamId } = {
      [getTeamName(AgenticTeamId.Marketing).toLowerCase()]: AgenticTeamId.Marketing,
      [getTeamName(AgenticTeamId.Research).toLowerCase()]: AgenticTeamId.Research,
      [getTeamName(AgenticTeamId.SoftwareDesign).toLowerCase()]: AgenticTeamId.SoftwareDesign,
      [getTeamName(AgenticTeamId.Sales).toLowerCase()]: AgenticTeamId.Sales,
      [getTeamName(AgenticTeamId.BusinessAnalysis).toLowerCase()]: AgenticTeamId.BusinessAnalysis,
      [getTeamName(AgenticTeamId.InvestigativeResearch).toLowerCase()]: AgenticTeamId.InvestigativeResearch,
    };
    return teamMappings[normalizedName] || null;
  }, [getTeamName]);


  // Combined logic to determine the actionable team for sending
  const getActionableTeamInfo = useCallback((record: PMORecord): { id: AgenticTeamId, name: string } | null => {
    console.log(`[PMORecordList] Getting actionable team for record ${record.id}:`, {
      agentIds: record.agentIds,
      agentIdsLength: record.agentIds?.length,
      pmoAssessmentLength: record.pmoAssessment?.length
    });

    // Priority 1: Use record.agentIds (selected teams from PMO assessment)
    if (record.agentIds && record.agentIds.length > 0) {
      const teamId = record.agentIds[0];
      const teamName = getTeamName(teamId);
      console.log(`[PMORecordList] Found team from agentIds: ${teamId} -> ${teamName}`);
      if (teamName !== 'Unknown Team') {
        return { id: teamId, name: teamName };
      }
    }

    // Priority 2: Fallback to parsing from pmoAssessment text (legacy support)
    const proposedTeamName = getProposedTeamNameFromAssessment(record.pmoAssessment ?? undefined);
    if (proposedTeamName) {
      const teamId = getTeamIdFromName(proposedTeamName);
      console.log(`[PMORecordList] Found team from assessment text: ${proposedTeamName} -> ${teamId}`);
      if (teamId) {
        return { id: teamId, name: getTeamName(teamId) };
      }
    }

    console.log(`[PMORecordList] No actionable team found for record ${record.id}`);
    return null;
  }, [getTeamName, getProposedTeamNameFromAssessment, getTeamIdFromName]);


  // Format date
  // Helper function to check if a category follows the PMO format pattern
  const isPMOFormatCategory = (category: string): boolean => {
    if (!category) return false;

    // Check if it matches the pattern: PMO - {title} - {id}
    // where {id} should be a UUID format
    const pmoPattern = /^PMO\s*-\s*.+\s*-\s*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    return pmoPattern.test(category);
  };

  // Helper function to get the appropriate category for a PMO record
  const getPMOCategory = (record: PMORecord): string => {
    // If the record already has a category that follows the PMO format, use it
    if (record.category && isPMOFormatCategory(record.category)) {
      return record.category;
    }

    // Otherwise, create a new category in the PMO format
    return `PMO - ${record.title} - ${record.id}`;
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Toggle record expansion
  const toggleExpand = (recordId: string) => {
    setExpandedRecordId(expandedRecordId === recordId ? null : recordId);
  };

  // View record details
  const viewRecord = (recordId: string) => {
    router.push(`/services/pmo/${recordId}`);
  };

  // Create PMO Requirement document
  const createPMORequirement = async (record: PMORecord) => {
    if (isCreatingRequirement) return;
    setIsCreatingRequirement(true);

    try {
      if (!user?.email) throw new Error('User email not found');
      const currentDate = new Date().toISOString();
      const proposedTeamForDoc = "Marketing";

      const requirementsContent = `
# Requirements Specification
**Date:** ${currentDate}
**Project Title:** ${record.title}
**Project ID:** ${record.id}
## 1. Project Overview
${record.description}
## 2. PMO Assessment
${record.pmoAssessment || 'No assessment available.'}
## 3. Team Selection
${record.teamSelectionRationale || 'No team selection rationale available.'}
## 4. Assigned Teams
${record.agentIds && record.agentIds.length > 0
  ? record.agentIds.map(teamId => getTeamName(teamId)).join(', ')
  : 'No teams assigned'}
## 5. Requirements
- Requirement 1: TBD
- Requirement 2: TBD
- Requirement 3: TBD
## 6. Approval Section (Placeholder for Sign-off)
- **Project Manager:** ______________ Date: ______________
- **Lead(s) of Assigned Team(s) (${proposedTeamForDoc} Lead):** ______________ Date: ______________
- **Product Owner/SME:** ______________ Date: ______________
Proposed Team Delegation
**Teams:** ${proposedTeamForDoc} **Rationale:** The ${proposedTeamForDoc} Team is best suited for this task as it involves marketing strategy, content creation, brand management, and market analysis.
`;

      // Get the appropriate category (reuse existing if it matches pattern, or create new)
      const categoryToUse = getPMOCategory(record);

      const response = await fetch('/api/pmo-process-document', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: `Requirements Specification - ${record.title} - (${record.id})`,
          content: requirementsContent,
          pmoId: record.id,
          userId: user.email,
          category: categoryToUse,
          metadata: {
            generatedAt: new Date().toISOString(),
            recordTitle: `${record.title} - ${record.id}`,
            recordDescription: record.description,
            recordStatus: record.status,
            recordPriority: record.priority,
          }
        }),
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.error || 'Failed to create requirements document');

      // Update PMO record category to match document category (only if it changed)
      if (record.category !== categoryToUse) {
        try {
          const updateResponse = await fetch('/api/pmo-record-update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              recordId: record.id,
              category: categoryToUse,
            }),
          });
          if (!updateResponse.ok) {
            console.warn('Failed to update PMO record category, but document was created successfully');
          }
        } catch (updateError) {
          console.warn('Error updating PMO record category:', updateError);
        }
      }

      setRequirementCreated(prev => ({ ...prev, [record.id]: true }));
      setSentToTeam(prev => ({ ...prev, [record.id]: false }));
      toast({
        title: "Requirements Document Created",
        description: "The requirements specification document has been created successfully.",
      });
    } catch (error) {
      console.error('Error creating requirements document:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create requirements document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingRequirement(false);
    }
  };

  // Handle investigative research requests
  const handleInvestigativeResearchRequest = async (record: PMORecord, teamName: string) => {
    try {
      // First, get intelligent journalist recommendations using LLM
      let selectedJournalistIds: string[] = [];

      try {
        console.log('Getting intelligent journalist recommendations for:', record.title);

        // Call the investigative research API to get LLM-based journalist recommendations
        const journalistResponse = await fetch('/api/investigative-research', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'recommend-journalists',
            title: record.title,
            description: record.description,
            investigationType: 'investigative',
            maxJournalists: 3
          }),
        });

        if (journalistResponse.ok) {
          const journalistResult = await journalistResponse.json();
          if (journalistResult.success && journalistResult.data?.length > 0) {
            selectedJournalistIds = journalistResult.data.map((j: any) => j.id);
            console.log('LLM selected journalists:', selectedJournalistIds);
          }
        }
      } catch (journalistError) {
        console.warn('Failed to get LLM journalist recommendations, using defaults:', journalistError);
      }

      // Fallback to default journalists if LLM selection failed
      if (selectedJournalistIds.length === 0) {
        selectedJournalistIds = ['investigative-journalist', 'financial-reporter', 'technology-analyst'];
        console.log('Using default journalist selection:', selectedJournalistIds);
      }

      // Extract investigative research configuration from PMO assessment or use defaults
      const investigativeConfig = {
        pmoId: record.id,
        title: record.title,
        description: record.description,
        investigationType: 'investigative', // InvestigationType.INVESTIGATIVE
        selectedJournalistIds: selectedJournalistIds, // LLM-selected or default journalists with their preferred models
        priority: record.priority || 'Medium',
        // Removed comparisonModels to ensure journalist personas with preferred models are used
        criteriaModel: 'claude-sonnet-4-0',
        optimizationModel: 'gpt-4o',
        assessmentModel: 'gpt-4.1-2025-04-14',
        consolidationModel: 'claude-sonnet-4-0',
        userId: user?.email
      };

      console.log('Starting investigative research with config:', investigativeConfig);
      console.log('Validation check - Title:', record.title);
      console.log('Validation check - Description:', record.description);
      console.log('Validation check - Combined text:', `${record.title} ${record.description || ''}`);

      // Call the investigative research API
      const response = await fetch('/api/investigative-research', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'conduct', // Changed from 'investigate' to 'conduct' to avoid duplicate storage
          ...investigativeConfig
        }),
      });

      const result = await response.json();
      if (!response.ok || !result.success) {
        throw new Error(result.error || 'Failed to start investigative research');
      }

      // Update PMO record to mark as sent to investigative research team
      try {
        const updateResponse = await fetch('/api/pmo-record-update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recordId: record.id,
            status: 'In Progress',
            assignedTeamNotified: true,
            notifiedAt: new Date().toISOString(),
            investigativeResearchId: result.data?.investigationId,
          }),
        });
        if (!updateResponse.ok) {
          console.warn('Failed to update PMO record status, but investigative research was started successfully');
        }
      } catch (updateError) {
        console.warn('Error updating PMO record status:', updateError);
      }

      setSentToTeam(prev => ({ ...prev, [record.id]: true }));

      // Start monitoring for investigative research completion
      console.log(`[PMORecordList] Starting completion monitoring for ${teamName} team processing`);
      startTeamCompletionMonitoring(record.id, teamName, result.data?.investigationId);

      toast({
        title: "Investigative Research Started",
        description: `The investigative research has been initiated by the ${teamName} team. You will be notified when the investigation is complete.`,
      });

    } catch (error) {
      console.error('Error starting investigative research:', error);
      throw error; // Re-throw to be handled by the calling function
    }
  };

  // Send requirements to delegated team (no longer creates strategic plan)
  const sendToDelegatedTeam = async (record: PMORecord) => {
    if (isSendingToTeam) return;

    const actionableTeam = getActionableTeamInfo(record);
    if (!actionableTeam) {
      toast({
        title: "Cannot Send",
        description: "No valid team could be determined for this action. Check PMO Assessment or assigned teams.",
        variant: "destructive",
      });
      return;
    }

    setIsSendingToTeam(true);

    try {
      if (!user?.email) throw new Error('User email not found');

      const { id: teamId, name: teamName } = actionableTeam;

      // Special handling for Investigative Research Team
      if (teamId === AgenticTeamId.InvestigativeResearch) {
        await handleInvestigativeResearchRequest(record, teamName);
        return;
      }

      // Get the appropriate category (reuse existing if it matches pattern, or create new)
      const categoryToUse = getPMOCategory(record);

      // Create team notification instead of strategic plan
      const response = await fetch('/api/pmo-notify-team', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pmoId: record.id,
          teamId: teamId,
          teamName: teamName,
          projectTitle: record.title,
          projectDescription: record.description,
          pmoAssessment: record.pmoAssessment,
          teamSelectionRationale: record.teamSelectionRationale,
          priority: record.priority,
          category: categoryToUse,
          userId: user.email,
          // Include full PMO record data for proper context
          pmoRecord: {
            id: record.id,
            title: record.title,
            description: record.description,
            status: record.status,
            priority: record.priority,
            createdAt: record.createdAt,
            updatedAt: record.updatedAt,
            createdBy: record.createdBy,
            category: record.category,
            sourceFile: record.sourceFile,
            fileName: record.fileName,
            customContext: record.customContext,
            contextFiles: record.contextFiles,
            contextCategories: record.contextCategories,
            pmoAssessment: record.pmoAssessment,
            agentIds: record.agentIds,
            summary: record.summary,
            teamSelectionRationale: record.teamSelectionRationale,
            resourceRecommendations: record.resourceRecommendations
          },
          metadata: {
            notifiedAt: new Date().toISOString(),
            recordTitle: record.title,
            recordDescription: record.description,
            recordStatus: record.status,
            recordPriority: record.priority,
            assignedTeam: teamName,
            assignedTeamId: teamId,
            requiresStrategicPlan: true
          }
        }),
      });

      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.error || 'Failed to notify team');

      // Update PMO record to mark as sent to team
      try {
        const updateResponse = await fetch('/api/pmo-record-update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recordId: record.id,
            status: 'In Progress',
            assignedTeamNotified: true,
            notifiedAt: new Date().toISOString(),
          }),
        });
        if (!updateResponse.ok) {
          console.warn('Failed to update PMO record status, but team was notified successfully');
        }
      } catch (updateError) {
        console.warn('Error updating PMO record status:', updateError);
      }

      setSentToTeam(prev => ({ ...prev, [record.id]: true }));

      // Start monitoring for team completion (for automatic tab navigation)
      console.log(`[PMORecordList] Starting completion monitoring for ${teamName} team processing`);
      startTeamCompletionMonitoring(record.id, teamName, result.requestId || result.collaborationData?.requestId);

      toast({
        title: "Requirements Sent to Team",
        description: `The requirements specification has been sent to the ${teamName} team. They will create their own strategic implementation plan.`,
      });
    } catch (error) {
      console.error('Error sending to delegated team:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send to delegated team. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSendingToTeam(false);
    }
  };

  // Delete record
  const confirmDelete = (recordId: string) => {
    setDeleteConfirmId(recordId);
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const handleDelete = async (recordId: string) => {
    setIsDeleting(true);
    try {
      if (!user?.email) throw new Error('User email not found');
      await deletePMORecord(user.email, recordId);
      setRequirementCreated(prev => { const newState = {...prev}; delete newState[recordId]; return newState; });
      setSentToTeam(prev => { const newState = {...prev}; delete newState[recordId]; return newState; });

      // Clear any monitoring intervals for this record
      if (monitoringIntervals[recordId]) {
        clearInterval(monitoringIntervals[recordId]);
        setMonitoringIntervals(prev => { const newState = {...prev}; delete newState[recordId]; return newState; });
      }

      if (expandedRecordId === recordId) setExpandedRecordId(null);
      onRefresh();
      toast({ title: "Record Deleted", description: "The PMO record has been successfully deleted." });
    } catch (error) {
      console.error('Error deleting PMO record:', error);
      toast({ title: "Error Deleting", description: "Failed to delete PMO record.", variant: "destructive" });
    } finally {
      setIsDeleting(false);
      setDeleteConfirmId(null);
    }
  };

  // Function to start monitoring for team completion
  const startTeamCompletionMonitoring = (pmoId: string, teamName: string, requestId?: string) => {
    console.log(`[PMORecordList] Starting completion monitoring for PMO ${pmoId}, team: ${teamName}, requestId: ${requestId}`);

    // Clear any existing interval for this PMO record
    if (monitoringIntervals[pmoId]) {
      clearInterval(monitoringIntervals[pmoId]);
    }

    let checkCount = 0;
    const maxChecks = 60; // Monitor for up to 5 minutes (60 checks * 5 seconds)

    const interval = setInterval(async () => {
      checkCount++;

      try {
        // Check if agent output exists for this request
        const response = await fetch(`/api/agent-outputs?requestId=${requestId}&limit=1`);

        if (response.ok) {
          const data = await response.json();

          // If we found an output, the processing is complete
          if (data && (data.id || data.results?.length > 0)) {
            console.log(`[PMORecordList] Team processing completed for PMO ${pmoId} - found agent output`);

            // Dispatch completion event based on team type
            if (teamName.toLowerCase().includes('investigative research')) {
              dispatchTabNavigationEvent('investigative-research-completed', {
                requestId,
                pmoId,
                success: true,
                agentType: 'InvestigativeResearch'
              });
            } else if (teamName.toLowerCase().includes('research')) {
              dispatchTabNavigationEvent('research-completed', {
                requestId,
                pmoId,
                success: true,
                agentType: 'Research'
              });
            } else if (teamName.toLowerCase().includes('marketing')) {
              dispatchTabNavigationEvent('marketing-completed', {
                requestId,
                pmoId,
                success: true,
                agentType: 'strategic-director'
              });
            } else {
              dispatchTabNavigationEvent('task-completed', {
                requestId,
                pmoId,
                teamName,
                success: true
              });
            }

            // Clear the interval
            clearInterval(interval);
            setMonitoringIntervals(prev => { const newState = {...prev}; delete newState[pmoId]; return newState; });
            return;
          }
        }

        // Stop monitoring after max checks
        if (checkCount >= maxChecks) {
          console.log(`[PMORecordList] Stopping completion monitoring for PMO ${pmoId} - max checks reached`);
          clearInterval(interval);
          setMonitoringIntervals(prev => { const newState = {...prev}; delete newState[pmoId]; return newState; });
        }

      } catch (error) {
        console.error(`[PMORecordList] Error checking completion status for PMO ${pmoId}:`, error);
      }
    }, 5000); // Check every 5 seconds

    // Store the interval
    setMonitoringIntervals(prev => ({ ...prev, [pmoId]: interval }));
  };

  // Cleanup intervals on unmount
  React.useEffect(() => {
    return () => {
      Object.values(monitoringIntervals).forEach(interval => {
        clearInterval(interval);
      });
    };
  }, [monitoringIntervals]);

  return (
    <div className="space-y-4">
      {records.map(record => {
        const actionableTeam = getActionableTeamInfo(record);

        return (
          <div
            key={record.id}
            className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700 hover:border-gray-600 transition-colors"
          >
            <div
              className="p-4 cursor-pointer"
              onClick={() => toggleExpand(record.id)}
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="flex items-start space-x-3">
                  {getStatusIcon(record.status)}
                  <div>
                    <h3 className="text-lg font-medium text-white">
                      {record.title}
                      <span className="text-sm text-gray-400 ml-2">- {record.id}</span>
                    </h3>
                    <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                      {record.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center mt-3 md:mt-0">
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${getPriorityColorClass(record.priority)}`}>
                    {record.priority}
                  </span>
                  <ChevronRight
                    className={`w-5 h-5 text-gray-400 ml-2 transition-transform ${
                      expandedRecordId === record.id ? 'rotate-90' : ''
                    }`}
                  />
                </div>
              </div>
            </div>

            {expandedRecordId === record.id && (
              <div className="px-4 pb-4 pt-0 border-t border-gray-700">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Created</h4>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {formatDate(record.createdAt)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Last Updated</h4>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {formatDate(record.updatedAt)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Status</h4>
                    <div className="flex items-center">
                      {getStatusIcon(record.status)}
                      <span className="text-sm text-white ml-2">
                        {record.status}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Assigned Teams</h4>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {record.agentIds && record.agentIds.length > 0
                          ? record.agentIds.map(teamId => getTeamName(teamId)).join(', ')
                          : 'No teams assigned'}
                      </span>
                    </div>
                  </div>
                </div>

                {record.pmoAssessment && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-1">PMO Assessment</h4>
                    <div className="text-sm text-gray-300 bg-gray-700/50 p-3 rounded-md prose prose-sm prose-invert max-w-none">
                      <MarkdownRenderer content={record.pmoAssessment ?? undefined} />
                    </div>
                  </div>
                )}

                {record.teamSelectionRationale && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Team Selection Rationale</h4>
                    <div className="text-sm text-gray-300 bg-gray-700/50 p-3 rounded-md prose prose-sm prose-invert max-w-none">
                      <MarkdownRenderer content={record.teamSelectionRationale ?? undefined} />
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2 mt-4">
                  <button
                    onClick={() => viewRecord(record.id)}
                    className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View Details
                  </button>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => createPMORequirement(record)}
                      className="flex items-center px-3 py-1.5 bg-blue-600/30 text-blue-300 rounded-md hover:bg-blue-600/40 transition-colors text-sm"
                      disabled={isCreatingRequirement || (requirementCreated[record.id] && !sentToTeam[record.id])}
                    >
                      <FileText className="w-4 h-4 mr-1" />
                      {isCreatingRequirement ? 'Creating...' : (requirementCreated[record.id] ? 'Recreate Requirement' : 'Save PMO Requirement')}
                    </button>
                    {requirementCreated[record.id] && !sentToTeam[record.id] && (
                      <button
                        onClick={() => sendToDelegatedTeam(record)}
                        className="flex items-center px-3 py-1.5 bg-green-600/30 text-green-300 rounded-md hover:bg-green-600/40 transition-colors text-sm"
                        disabled={isSendingToTeam || !actionableTeam}
                      >
                        {isSendingToTeam ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-1" />
                            {actionableTeam
                              ? `Send to ${actionableTeam.name}`
                              : 'Send to Team'
                            }
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  {deleteConfirmId === record.id ? (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-red-400">Confirm delete?</span>
                      <button
                        onClick={() => handleDelete(record.id)}
                        className="flex items-center px-3 py-1.5 bg-red-600/30 text-red-300 rounded-md hover:bg-red-600/40 transition-colors text-sm"
                        disabled={isDeleting}
                      >
                        {isDeleting ? 'Deleting...' : 'Yes'}
                      </button>
                      <button
                        onClick={cancelDelete}
                        className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                      >
                        No
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => confirmDelete(record.id)}
                      className="flex items-center px-3 py-1.5 bg-red-600/30 text-red-300 rounded-md hover:bg-red-600/40 transition-colors text-sm"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  );
};

export default PMORecordList;