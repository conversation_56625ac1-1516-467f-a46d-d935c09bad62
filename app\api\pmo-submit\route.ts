import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { createPMORecordFromForm } from '../../../lib/firebase/pmoCollection';
import { PMOAssessmentAgent } from '../../../lib/agents/pmo/PMOAssessmentAgent';
import { PMOFormInput, AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
import { QueryDocumentsAgent } from '../../../components/Agents/QueryDocumentsAgent';
import { StrategicDirectorAgent } from 'lib/agents/marketing/StrategicDirectorAgent';
import { createLlmService } from '../../../lib/tools/llmServiceAdapter';
import { processWithGroq } from '../../../lib/tools/groq-ai';
import { DEFAULT_COMPARISON_MODELS } from '../../../lib/agents/investigative/constants';

/**
 * Intelligent team selection using Groq LLM (shared with assessment endpoint)
 */
async function determineTeamSelection(title: string, description: string): Promise<{
  selectedTeams: AgenticTeamId[];
  rationale: string;
  isInvestigativeResearch: boolean;
}> {
  const teamSelectionPrompt = `
You are an expert PMO (Project Management Office) analyst tasked with determining the most appropriate team(s) for a project request.

Available Teams:
1. **Marketing Team** - Marketing strategy, content creation, brand management, campaigns, market analysis
2. **Research Team** - Data collection, analysis, research reports, market research, academic research
3. **Software Design Team** - Software development, UI/UX design, coding, technical implementation, apps
4. **Sales Team** - Sales strategies, client relationships, revenue generation, business development
5. **Business Analysis Team** - Business process analysis, requirements gathering, strategic planning, operations
6. **Investigative Research Team** - Comprehensive investigative analysis, multi-source verification, deep-dive research, exposing irregularities, journalistic investigation, fact-checking, corruption analysis, financial fraud detection

Project Details:
Title: "${title}"
Description: "${description}"

Instructions:
1. Analyze the project requirements carefully
2. Select the MOST APPROPRIATE team(s) based on the core activities required
3. Provide a clear rationale for your selection
4. Determine if this is investigative research (requires exposing, uncovering, investigating irregularities, corruption, fraud, or comprehensive fact-checking)

Respond in this exact JSON format:
{
  "selectedTeam": "TeamName",
  "rationale": "Clear explanation of why this team is best suited",
  "isInvestigativeResearch": true/false,
  "confidence": "High/Medium/Low"
}

Only select ONE primary team. Choose the team whose core competencies most closely match the project's primary objectives.
`;

  try {
    const response = await processWithGroq({
      prompt: teamSelectionPrompt,
      model: "deepseek-r1-distill-llama-70b"
    });

    // Parse the JSON response
    const cleanResponse = response.replace(/```json\n?|\n?```/g, '').trim();
    const analysis = JSON.parse(cleanResponse);

    // Map team name to AgenticTeamId
    const teamMapping: { [key: string]: AgenticTeamId } = {
      'Marketing Team': AgenticTeamId.Marketing,
      'Marketing': AgenticTeamId.Marketing,
      'Research Team': AgenticTeamId.Research,
      'Research': AgenticTeamId.Research,
      'Software Design Team': AgenticTeamId.SoftwareDesign,
      'Software Design': AgenticTeamId.SoftwareDesign,
      'Sales Team': AgenticTeamId.Sales,
      'Sales': AgenticTeamId.Sales,
      'Business Analysis Team': AgenticTeamId.BusinessAnalysis,
      'Business Analysis': AgenticTeamId.BusinessAnalysis,
      'Investigative Research Team': AgenticTeamId.InvestigativeResearch,
      'Investigative Research': AgenticTeamId.InvestigativeResearch
    };

    const selectedTeamId = teamMapping[analysis.selectedTeam] || AgenticTeamId.Research; // Default fallback

    return {
      selectedTeams: [selectedTeamId],
      rationale: analysis.rationale || "Team selected based on project requirements analysis.",
      isInvestigativeResearch: analysis.isInvestigativeResearch || false
    };

  } catch (error) {
    console.error('Error in LLM team selection:', error);

    // Fallback to simple keyword detection
    const descriptionLower = description.toLowerCase();
    const titleLower = title.toLowerCase();
    const combined = `${titleLower} ${descriptionLower}`;

    if (combined.includes('investigat') || combined.includes('expose') || combined.includes('corruption') ||
        combined.includes('fraud') || combined.includes('scandal') || combined.includes('uncover')) {
      return {
        selectedTeams: [AgenticTeamId.InvestigativeResearch],
        rationale: "Investigative Research Team selected based on keywords indicating investigative analysis requirements.",
        isInvestigativeResearch: true
      };
    } else if (combined.includes('market') || combined.includes('brand') || combined.includes('campaign')) {
      return {
        selectedTeams: [AgenticTeamId.Marketing],
        rationale: "Marketing Team selected based on marketing-related keywords.",
        isInvestigativeResearch: false
      };
    } else if (combined.includes('software') || combined.includes('develop') || combined.includes('code') || combined.includes('app')) {
      return {
        selectedTeams: [AgenticTeamId.SoftwareDesign],
        rationale: "Software Design Team selected based on development-related keywords.",
        isInvestigativeResearch: false
      };
    } else {
      return {
        selectedTeams: [AgenticTeamId.Research],
        rationale: "Research Team selected as default for analysis and research tasks.",
        isInvestigativeResearch: false
      };
    }
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const {
      projectName,
      description,
      selectedProvider, // Used to create the LLM service
      selectedModel,    // Used to specify the model for the LLM service
      customContext,
      selectedDocumentId,
      selectedCategory,
      generatedPmoAssessment, // This is the requirements specification generated in the previous step
      selectedTeamsFromAssessment, // Team selection data from the assessment API
      teamSelectionRationale, // Team selection rationale from the assessment API
      isInvestigativeResearch,
      investigativeOptions
    } = body;

    // Validate required fields
    if (!projectName || !description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create an LLM service based on the selected provider (or default to openai)
    const llmService = createLlmService(selectedProvider || 'openai');

    // Create the PMO assessment agent with the LLM service
    const pmoAgent = new PMOAssessmentAgent({
      userId: session.user.email,
      includeExplanation: false,
      streamResponse: false
    }, llmService);

    // Create and set the query documents agent
    // Corrected: Constructor expects a string (userId), not an object.
    const queryDocumentsAgent = new QueryDocumentsAgent({
      userId: session.user.email
    });
    // If 'includeExplanation: false' was intended for this agent,
    // it would need to be set via a property or method if available, e.g.:
    // queryDocumentsAgent.includeExplanation = false;
    // For this fix, we only address the constructor error.
    pmoAgent.setQueryDocumentsAgent(queryDocumentsAgent);

    // Create and set the strategic director agent
    // Corrected: Constructor expects a string (userId), not an object.
    const strategicDirectorAgent = new StrategicDirectorAgent(session.user.email);
    // If 'includeExplanation: false' was intended for this agent,
    // it would need to be set via a property or method if available, e.g.:
    // strategicDirectorAgent.includeExplanation = false;
    // For this fix, we only address the constructor error.
    pmoAgent.setStrategicDirectorAgent(strategicDirectorAgent);

    // Create the form input
    const formInput: PMOFormInput = {
      title: projectName,
      description,
      priority: 'Medium', // Hardcoded priority
      category: selectedCategory || 'Unknown', // Use selected category if available, otherwise 'Unknown'
      modelName: selectedModel, // Pass the selected model to the LLM service
      modelProvider: selectedProvider, // Pass the selected provider to the LLM service
      contextOptions: {
        customContext: customContext || null,
        fileIds: selectedDocumentId ? [selectedDocumentId] : null,
        categoryIds: selectedCategory ? [selectedCategory] : null
      }
    };

    // We'll use the pre-generated requirements specification if available
    let pmoAssessment = '';
    let selectedTeams: AgenticTeamId[] = [];
    let requirementsDocumentId: string | undefined;

    if (generatedPmoAssessment) {
      // Use the pre-generated requirements specification
      pmoAssessment = generatedPmoAssessment;

      // Use team selection from assessment if available, otherwise fall back to LLM selection
      if (selectedTeamsFromAssessment && selectedTeamsFromAssessment.length > 0) {
        console.log('Using team selection from assessment API...');
        selectedTeams = selectedTeamsFromAssessment.map(teamId => teamId as AgenticTeamId);
        console.log(`PMO Submit - Using assessment teams: ${selectedTeams.map(t => Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === t) || 'Unknown').join(', ')}`);
      } else {
        // Fallback to LLM-based intelligent team selection
        console.log('No team selection from assessment, determining team selection using Groq LLM for PMO submission...');
        const teamSelection = await determineTeamSelection(projectName, description);
        selectedTeams = teamSelection.selectedTeams;
        console.log(`PMO Submit - Fallback team selection result: ${selectedTeams.map(t => Object.keys(AgenticTeamId).find(key => (AgenticTeamId as any)[key] === t) || 'Unknown').join(', ')}`);
      }

      // Generate PDF and save agent output
      if (pmoAgent.hasPDFGenerator()) {
        try {
          const pdfResult = await pmoAgent.generatePDF(projectName, pmoAssessment);
          if (pdfResult.success && pdfResult.fileUrl) {
            const outputResult = await pmoAgent.saveAgentOutput(
              projectName,
              pmoAssessment,
              pdfResult.fileUrl,
              selectedTeams,
              'Medium'
            );
            requirementsDocumentId = outputResult.id;
          }
        } catch (error) {
          console.error('Error generating PDF or saving agent output:', error);
        }
      }
    } else {
      // If no pre-generated requirements specification, generate it now
      const assessmentResult = await pmoAgent.generateAssessment(formInput);
      pmoAssessment = assessmentResult.pmoAssessment;
      selectedTeams = assessmentResult.selectedTeams;
      requirementsDocumentId = assessmentResult.requirementsDocumentId;
    }

    // Prepare data for PMO record creation
    const formData = {
      title: projectName,
      description,
      priority: 'Medium' as const,
      category: selectedCategory || 'Unknown', // Use selected category if available, otherwise 'Unknown'
      sourceFile: selectedDocumentId || null, // Use null instead of undefined
      fileName: selectedCategory || `${projectName} - Requirements Specification`, // Ensure fileName is always a string
      customContext: customContext || null, // Use null instead of undefined
      selectedFileId: selectedDocumentId || null, // Use null instead of undefined
      selectedCategory: selectedCategory || null, // Use null instead of undefined
      pmoAssessment: pmoAssessment
    };

    // Create the PMO record in Firebase
    const pmoId = await createPMORecordFromForm(session.user.email, formData, selectedTeams);

    // If this is an investigative research request, store the configuration for later use
    let investigativeResearchConfig = null;
    if (isInvestigativeResearch && selectedTeams.includes(AgenticTeamId.InvestigativeResearch)) {
      investigativeResearchConfig = {
        pmoId: pmoId,
        title: projectName,
        description: description,
        investigationType: 'investigative', // Default type, can be refined later
        priority: 'Medium',
        comparisonModels: investigativeOptions?.comparisonModels || DEFAULT_COMPARISON_MODELS,
        criteriaModel: investigativeOptions?.criteriaModel || 'claude-sonnet-4-0',
        optimizationModel: investigativeOptions?.optimizationModel || 'gpt-4o',
        assessmentModel: investigativeOptions?.assessmentModel || 'claude-sonnet-4-0',
        consolidationModel: investigativeOptions?.consolidationModel || 'claude-sonnet-4-0',
        userId: session.user.email
      };

      console.log('Investigative research configuration prepared:', investigativeResearchConfig);
    }

    // Return the result
    return NextResponse.json({
      success: true,
      pmoId,
      pmoAssessment: pmoAssessment,
      selectedTeams: selectedTeams,
      requirementsDocumentId: requirementsDocumentId,
      isInvestigativeResearch: isInvestigativeResearch,
      investigativeResearchConfig: investigativeResearchConfig
    });
  } catch (error: any) {
    console.error('Error submitting PMO request:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to submit PMO request' },
      { status: 500 }
    );
  }
}
